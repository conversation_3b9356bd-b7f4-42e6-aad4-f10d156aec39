#!/usr/bin/env python3
"""
坐标验证工具

验证螺钉末端坐标在不同坐标系中的表示
"""

import sys
import os
import json
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def verify_coordinates():
    """验证坐标"""
    try:
        from utils.io_utils import DataLoader
        from utils.geometry import Point3D
        
        print("=" * 60)
        print("坐标验证工具")
        print("=" * 60)
        
        # 加载CT图像获取变换信息
        data_dir = os.path.join(current_dir, 'examples', 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        
        # 获取图像信息
        origin = ct_image.GetOrigin()
        spacing = ct_image.GetSpacing()
        direction = ct_image.GetDirection()
        size = ct_image.GetSize()
        
        print("📊 CT图像信息:")
        print(f"  原点 (Origin): ({origin[0]:.2f}, {origin[1]:.2f}, {origin[2]:.2f})")
        print(f"  间距 (Spacing): ({spacing[0]:.3f}, {spacing[1]:.3f}, {spacing[2]:.3f})")
        print(f"  尺寸 (Size): {size}")
        print(f"  方向矩阵 (Direction): {direction}")
        
        # 读取规划结果
        result_file = os.path.join(current_dir, 'test_ring_sampling_result.json')
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        print("\n🔧 螺钉坐标验证:")
        print("-" * 60)
        
        if 'screw_paths' in result_data:
            for screw_name, path_data in result_data['screw_paths'].items():
                start_point = Point3D(
                    path_data['start_point']['x'],
                    path_data['start_point']['y'],
                    path_data['start_point']['z']
                )
                end_point = Point3D(
                    path_data['end_point']['x'],
                    path_data['end_point']['y'],
                    path_data['end_point']['z']
                )
                
                print(f"\n螺钉 {screw_name}:")
                print(f"  起始点 (物理坐标): ({start_point.x:.2f}, {start_point.y:.2f}, {start_point.z:.2f})")
                print(f"  末端点 (物理坐标): ({end_point.x:.2f}, {end_point.y:.2f}, {end_point.z:.2f})")
                
                # 转换为体素坐标
                start_voxel = ct_image.TransformPhysicalPointToIndex([start_point.x, start_point.y, start_point.z])
                end_voxel = ct_image.TransformPhysicalPointToIndex([end_point.x, end_point.y, end_point.z])
                
                print(f"  起始点 (体素坐标): ({start_voxel[0]}, {start_voxel[1]}, {start_voxel[2]})")
                print(f"  末端点 (体素坐标): ({end_voxel[0]}, {end_voxel[1]}, {end_voxel[2]})")
                
                # 计算路径长度
                import math
                length = math.sqrt(
                    (end_point.x - start_point.x)**2 + 
                    (end_point.y - start_point.y)**2 + 
                    (end_point.z - start_point.z)**2
                )
                print(f"  路径长度: {length:.2f} mm")
                print(f"  设定长度: {path_data['length']:.2f} mm")
                
                # 计算方向向量
                direction_vec = [
                    end_point.x - start_point.x,
                    end_point.y - start_point.y,
                    end_point.z - start_point.z
                ]
                length_vec = math.sqrt(sum(x*x for x in direction_vec))
                direction_normalized = [x/length_vec for x in direction_vec]
                
                print(f"  方向向量: ({direction_normalized[0]:.3f}, {direction_normalized[1]:.3f}, {direction_normalized[2]:.3f})")
                
                # 检查是否在图像范围内
                start_in_bounds = all(0 <= start_voxel[i] < size[i] for i in range(3))
                end_in_bounds = all(0 <= end_voxel[i] < size[i] for i in range(3))
                
                print(f"  起始点在图像内: {'✅' if start_in_bounds else '❌'}")
                print(f"  末端点在图像内: {'✅' if end_in_bounds else '❌'}")
        
        # 生成Slicer标记点文件
        print(f"\n📝 生成Slicer标记点文件:")
        print("-" * 60)
        
        markups_data = {
            "@schema": "https://raw.githubusercontent.com/slicer/slicer/master/Modules/Loadable/Markups/Resources/Schema/markups-schema-v1.0.0.json#",
            "markups": [
                {
                    "type": "Fiducial",
                    "coordinateSystem": "LPS",
                    "controlPoints": []
                }
            ]
        }
        
        if 'screw_paths' in result_data:
            for screw_name, path_data in result_data['screw_paths'].items():
                # 起始点
                start_point = [
                    path_data['start_point']['x'],
                    path_data['start_point']['y'],
                    path_data['start_point']['z']
                ]
                
                # 末端点
                end_point = [
                    path_data['end_point']['x'],
                    path_data['end_point']['y'],
                    path_data['end_point']['z']
                ]
                
                # 添加起始点
                markups_data["markups"][0]["controlPoints"].append({
                    "id": f"{screw_name}_start",
                    "label": f"{screw_name}_start",
                    "description": f"螺钉{screw_name}起始点",
                    "associatedNodeID": "",
                    "position": start_point,
                    "orientation": [-1.0, -0.0, -0.0, -0.0, -1.0, -0.0, 0.0, 0.0, 1.0],
                    "selected": True,
                    "locked": False,
                    "visibility": True,
                    "positionStatus": "defined"
                })
                
                # 添加末端点
                markups_data["markups"][0]["controlPoints"].append({
                    "id": f"{screw_name}_end",
                    "label": f"{screw_name}_end",
                    "description": f"螺钉{screw_name}末端点",
                    "associatedNodeID": "",
                    "position": end_point,
                    "orientation": [-1.0, -0.0, -0.0, -0.0, -1.0, -0.0, 0.0, 0.0, 1.0],
                    "selected": True,
                    "locked": False,
                    "visibility": True,
                    "positionStatus": "defined"
                })
        
        # 保存标记点文件
        markups_file = os.path.join(current_dir, 'screw_markups.mrk.json')
        with open(markups_file, 'w', encoding='utf-8') as f:
            json.dump(markups_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已生成Slicer标记点文件: {markups_file}")
        print(f"   在Slicer中加载此文件来验证坐标位置")
        
        # 建议
        print(f"\n💡 验证建议:")
        print("-" * 60)
        print(f"1. 在3D Slicer中加载CT图像: {ct_file}")
        print(f"2. 加载标记点文件: {markups_file}")
        print(f"3. 检查标记点位置是否与预期一致")
        print(f"4. 如果位置不对，可能需要坐标系转换")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = verify_coordinates()
    
    if success:
        print("\n🎉 验证完成！")
    else:
        print("\n❌ 验证失败")
        sys.exit(1)

if __name__ == "__main__":
    main()

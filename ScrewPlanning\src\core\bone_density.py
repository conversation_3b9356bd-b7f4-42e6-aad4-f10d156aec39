"""
骨密度计算模块

基于公式法计算骨密度：QCT = 17.8 + 0.7 × HU
"""

import numpy as np
import SimpleITK as sitk
from typing import List, Tuple, Optional
import logging
import math

try:
    from ..utils.geometry import Point3D, ScrewPath, Vector3D
except ImportError:
    from utils.geometry import Point3D, ScrewPath, Vector3D


class BoneDensityCalculator:
    """骨密度计算器"""

    def __init__(self, ct_image: sitk.Image, mask_image: Optional[sitk.Image] = None):
        """
        初始化骨密度计算器

        Args:
            ct_image: CT图像（SimpleITK格式）
            mask_image: 肩胛骨掩膜图像（可选，SimpleITK格式）
        """
        self.ct_image = ct_image
        self.ct_array = sitk.GetArrayFromImage(ct_image)
        self.spacing = ct_image.GetSpacing()
        self.origin = ct_image.GetOrigin()
        self.direction = ct_image.GetDirection()

        # 处理掩膜图像
        self.mask_image = mask_image
        self.mask_array = None
        if mask_image is not None:
            self.mask_array = sitk.GetArrayFromImage(mask_image)
            logging.info(f"肩胛骨掩膜已加载，尺寸: {self.mask_array.shape}")

        # 公式法参数：QCT = 17.8 + 0.7 × HU
        self.formula_intercept = 17.8
        self.formula_slope = 0.7

        logging.info(f"骨密度计算初始化完成")
        logging.info(f"CT图像尺寸: {self.ct_array.shape}")
        logging.info(f"体素间距: {self.spacing}")
    
    def hu_to_bone_density(self, hu_value: float) -> float:
        """
        将HU值转换为骨密度值（公式法）
        
        Args:
            hu_value: HU值
            
        Returns:
            骨密度值 (mg/cc)
        """
        return self.formula_intercept + self.formula_slope * hu_value
    
    def world_to_image_coordinates(self, world_point: Point3D) -> Tuple[int, int, int]:
        """
        将世界坐标转换为图像坐标
        
        Args:
            world_point: 世界坐标点
            
        Returns:
            图像坐标 (i, j, k)
        """
        # 转换为SimpleITK的物理坐标
        physical_point = [world_point.x, world_point.y, world_point.z]
        
        # 转换为图像索引
        image_index = self.ct_image.TransformPhysicalPointToIndex(physical_point)
        
        # SimpleITK使用(x,y,z)顺序，numpy数组使用(z,y,x)顺序
        return image_index[2], image_index[1], image_index[0]

    def is_point_in_mask(self, point: Point3D) -> bool:
        """
        检查点是否在肩胛骨掩膜内

        Args:
            point: 3D坐标点

        Returns:
            True如果点在掩膜内，False否则（如果没有掩膜则返回True）
        """
        if self.mask_array is None:
            # 如果没有掩膜，认为所有点都有效
            return True

        try:
            i, j, k = self.world_to_image_coordinates(point)

            # 检查坐标是否在掩膜范围内
            if (0 <= i < self.mask_array.shape[0] and
                0 <= j < self.mask_array.shape[1] and
                0 <= k < self.mask_array.shape[2]):
                # 检查掩膜值是否大于0（表示在肩胛骨内）
                return float(self.mask_array[i, j, k]) > 0
            else:
                return False
        except Exception as e:
            logging.warning(f"检查掩膜时出错: {e}")
            return False

    def get_hu_value_at_point(self, point: Point3D) -> Optional[float]:
        """
        获取指定点的HU值

        Args:
            point: 3D坐标点

        Returns:
            HU值，如果点在图像外或肩胛骨掩膜外则返回None
        """
        try:
            i, j, k = self.world_to_image_coordinates(point)

            # 检查坐标是否在图像范围内
            if (0 <= i < self.ct_array.shape[0] and
                0 <= j < self.ct_array.shape[1] and
                0 <= k < self.ct_array.shape[2]):

                # 检查是否在肩胛骨掩膜内
                if not self.is_point_in_mask(point):
                    return None

                return float(self.ct_array[i, j, k])
            else:
                return None
        except Exception as e:
            logging.warning(f"获取HU值时出错: {e}")
            return None
    
    def get_bone_density_at_point(self, point: Point3D) -> Optional[float]:
        """
        获取指定点的骨密度值
        
        Args:
            point: 3D坐标点
            
        Returns:
            骨密度值 (mg/cc)，如果点在图像外则返回None
        """
        hu_value = self.get_hu_value_at_point(point)
        if hu_value is not None:
            return self.hu_to_bone_density(hu_value)
        return None
    
    def generate_ring_sampling_points(self, center_point: Point3D, direction_vector: Vector3D,
                                     radius: float, num_ring_points: int = 12) -> List[Point3D]:
        """
        在垂直于给定方向的平面上生成圆环采样点

        Args:
            center_point: 圆环中心点
            direction_vector: 路径方向向量（已归一化）
            radius: 圆环半径（螺钉半径）
            num_ring_points: 圆环上的采样点数量

        Returns:
            圆环上的采样点列表
        """
        # 创建垂直于方向向量的两个正交向量
        if abs(direction_vector.x) < 0.9:
            temp_vector = Vector3D(1, 0, 0)
        else:
            temp_vector = Vector3D(0, 1, 0)

        # 计算两个正交向量
        perpendicular1 = direction_vector.cross(temp_vector).normalize()
        perpendicular2 = direction_vector.cross(perpendicular1).normalize()

        ring_points = []
        for i in range(num_ring_points):
            # 计算角度
            theta = 2 * math.pi * i / num_ring_points
            offset_vector = (perpendicular1 * math.cos(theta) + perpendicular2 * math.sin(theta)) * radius

            ring_point_array = center_point.to_array() + offset_vector.to_array()
            ring_point = Point3D.from_array(ring_point_array)
            ring_points.append(ring_point)

        return ring_points

    def evaluate_path(self, path: ScrewPath,
                     integration_resolution: int = 30,
                     ring_sampling_points: int = 12) -> ScrewPath:
        """
        评估螺钉路径（圆环采样方法）

        在螺钉路径上进行采样，在每个采样点处生成垂直于路径的圆环，计算圆环上各点的骨密度，然后进行积分计算。

        Args:
            path: 螺钉路径
            integration_resolution: 路径积分分辨率（沿路径的采样点数）
            ring_sampling_points: 每个圆环上的采样点数量

        Returns:
            更新后的螺钉路径（包含评估结果）
        """
        # 获取路径上的中心线采样点
        center_points = path.get_points_along_path(integration_resolution)
        direction_vector = path.get_direction_vector()

        total_integral = 0.0
        valid_rings = 0
        path_is_valid = True

        for center_point in center_points:
            # 生成当前位置的圆环采样点
            ring_points = self.generate_ring_sampling_points(
                center_point, direction_vector, path.radius, ring_sampling_points)

            # 计算圆环上各点的骨密度
            ring_bone_densities = []
            valid_ring_points = 0

            for ring_point in ring_points:
                # 检查点是否在肩胛骨掩膜内
                if not self.is_point_in_mask(ring_point):
                    continue

                bone_density = self.get_bone_density_at_point(ring_point)
                if bone_density is not None:
                    ring_bone_densities.append(bone_density)
                    valid_ring_points += 1

            # 如果圆环上有效点太少，认为这个圆环无效
            if valid_ring_points < ring_sampling_points * 0.3:  # 至少30%的点有效
                path_is_valid = False
                continue

            # 计算圆环的平均骨密度
            if ring_bone_densities:
                ring_average_density = sum(ring_bone_densities) / len(ring_bone_densities)
                total_integral += ring_average_density
                valid_rings += 1

        # 计算最终的骨密度积分
        if valid_rings < integration_resolution * 0.5:  # 至少50%的圆环有效
            logging.warning(f"路径有效圆环过少: {valid_rings}/{integration_resolution}")
            path.bone_density_integral = 0.0
            path.is_valid = False
        else:
            if valid_rings > 0:
                # 计算平均骨密度并乘以路径长度得到积分值
                average_density = total_integral / valid_rings
                path_length = path.get_path_length()
                path.bone_density_integral = average_density * path_length
                path.is_valid = path_is_valid
            else:
                path.bone_density_integral = 0.0
                path.is_valid = False

        # 计算安全评分（基于骨密度积分和有效性）
        if path.is_valid and path.bone_density_integral > 0:
            path.safety_score = path.bone_density_integral
        else:
            path.safety_score = 0.0

        return path
    
    def quick_endpoint_filter(self, path: ScrewPath) -> bool:
        """
        第一阶段快速筛选：检查路径终点是否在肩胛骨mask内

        Args:
            path: 螺钉路径

        Returns:
            True如果终点在mask内，False否则
        """
        return self.is_point_in_mask(path.end_point)

    def get_cylinder_bounding_box(self, start_point: Point3D, end_point: Point3D,
                                radius: float) -> Tuple[Tuple[int, int, int], Tuple[int, int, int]]:
        """
        计算圆柱体在图像坐标系中的边界框

        Args:
            start_point: 圆柱体起点
            end_point: 圆柱体终点
            radius: 圆柱体半径

        Returns:
            边界框的最小和最大坐标 ((min_i, min_j, min_k), (max_i, max_j, max_k))
        """
        # 转换为图像坐标
        try:
            start_i, start_j, start_k = self.world_to_image_coordinates(start_point)
            end_i, end_j, end_k = self.world_to_image_coordinates(end_point)
        except Exception as e:
            logging.warning(f"坐标转换失败: {e}")
            return ((0, 0, 0), (0, 0, 0))

        # 计算半径在图像坐标系中的大小（取最大值以确保覆盖）
        radius_voxels = int(np.ceil(radius / min(self.spacing))) + 1

        # 计算边界框
        min_i = max(0, min(start_i, end_i) - radius_voxels)
        max_i = min(self.ct_array.shape[0] - 1, max(start_i, end_i) + radius_voxels)
        min_j = max(0, min(start_j, end_j) - radius_voxels)
        max_j = min(self.ct_array.shape[1] - 1, max(start_j, end_j) + radius_voxels)
        min_k = max(0, min(start_k, end_k) - radius_voxels)
        max_k = min(self.ct_array.shape[2] - 1, max(start_k, end_k) + radius_voxels)

        return ((min_i, min_j, min_k), (max_i, max_j, max_k))

    def create_cylinder_mask(self, path: ScrewPath) -> Tuple[np.ndarray, Tuple[int, int, int]]:
        """
        创建圆柱体的体素化mask（向量化优化版本）

        Args:
            path: 螺钉路径

        Returns:
            圆柱体mask数组和边界框起始坐标
        """
        # 获取边界框
        (min_i, min_j, min_k), (max_i, max_j, max_k) = self.get_cylinder_bounding_box(
            path.start_point, path.end_point, path.radius)

        if max_i <= min_i or max_j <= min_j or max_k <= min_k:
            logging.warning("无效的边界框")
            return np.array([]), (0, 0, 0)

        # 创建局部mask数组
        local_shape = (max_i - min_i + 1, max_j - min_j + 1, max_k - min_k + 1)

        # 计算圆柱体参数
        start_array = path.start_point.to_array()
        end_array = path.end_point.to_array()
        cylinder_vector = end_array - start_array
        cylinder_length = np.linalg.norm(cylinder_vector)

        if cylinder_length == 0:
            logging.warning("圆柱体长度为0")
            return np.zeros(local_shape, dtype=bool), (min_i, min_j, min_k)

        cylinder_direction = cylinder_vector / cylinder_length

        # 向量化计算：批量生成所有体素的图像坐标
        i_coords, j_coords, k_coords = np.meshgrid(
            np.arange(local_shape[0]),
            np.arange(local_shape[1]),
            np.arange(local_shape[2]),
            indexing='ij'
        )

        # 转换为全局图像坐标
        global_i_coords = i_coords + min_i
        global_j_coords = j_coords + min_j
        global_k_coords = k_coords + min_k

        # 批量转换为物理坐标（使用仿射变换）
        # 获取图像的仿射变换参数
        origin = np.array(self.ct_image.GetOrigin())
        spacing = np.array(self.ct_image.GetSpacing())
        direction_matrix = np.array(self.ct_image.GetDirection()).reshape(3, 3)

        # 构建物理坐标（向量化计算）
        # 注意：SimpleITK使用(x,y,z)顺序，numpy使用(z,y,x)顺序
        image_coords = np.stack([
            global_k_coords.flatten(),  # x坐标
            global_j_coords.flatten(),  # y坐标
            global_i_coords.flatten()   # z坐标
        ], axis=1)

        # 应用仿射变换：physical = origin + direction_matrix @ (spacing * image_coords)
        scaled_coords = image_coords * spacing[np.newaxis, :]
        physical_coords = origin[np.newaxis, :] + (direction_matrix @ scaled_coords.T).T

        # 重新整形为3D数组
        physical_coords = physical_coords.reshape(local_shape + (3,))

        # 向量化计算点到圆柱轴线的距离
        # 计算从起点到各个点的向量
        points_to_start = physical_coords - start_array[np.newaxis, np.newaxis, np.newaxis, :]

        # 计算投影长度（点积）
        projection_lengths = np.sum(points_to_start * cylinder_direction[np.newaxis, np.newaxis, np.newaxis, :], axis=3)

        # 检查点是否在圆柱长度范围内
        length_mask = (projection_lengths >= 0) & (projection_lengths <= cylinder_length)

        # 计算投影点
        projection_points = (start_array[np.newaxis, np.newaxis, np.newaxis, :] +
                           projection_lengths[:, :, :, np.newaxis] *
                           cylinder_direction[np.newaxis, np.newaxis, np.newaxis, :])

        # 计算点到轴线的距离
        distances_to_axis = np.linalg.norm(physical_coords - projection_points, axis=3)

        # 检查是否在圆柱半径内
        radius_mask = distances_to_axis <= path.radius

        # 组合所有条件
        cylinder_mask = length_mask & radius_mask

        return cylinder_mask, (min_i, min_j, min_k)

    def batch_evaluate_paths(self, paths: List[ScrewPath],
                           integration_resolution: int = 30,
                           ring_sampling_points: int = 12) -> List[ScrewPath]:
        """
        批量评估螺钉路径（圆环采样方法）

        Args:
            paths: 螺钉路径列表
            integration_resolution: 路径积分分辨率（沿路径的采样点数）
            ring_sampling_points: 每个圆环上的采样点数量

        Returns:
            评估后的螺钉路径列表
        """
        evaluated_paths = []

        for i, path in enumerate(paths):
            if i % 100 == 0:
                logging.info(f"评估路径进度: {i}/{len(paths)} (圆环采样方法)")

            evaluated_path = self.evaluate_path(path, integration_resolution, ring_sampling_points)
            evaluated_paths.append(evaluated_path)

        logging.info(f"路径评估完成，共评估 {len(evaluated_paths)} 条路径")

        # 统计有效路径
        valid_paths = [p for p in evaluated_paths if p.is_valid]
        logging.info(f"有效路径数量: {len(valid_paths)}/{len(evaluated_paths)}")

        return evaluated_paths

    def calculate_cylinder_coverage(self, path: ScrewPath) -> float:
        """
        计算圆柱体与肩胛骨掩膜的覆盖率

        Args:
            path: 螺钉路径

        Returns:
            覆盖率 (0.0 - 1.0)
        """
        # 创建圆柱体mask
        cylinder_mask, (min_i, min_j, min_k) = self.create_cylinder_mask(path)

        if cylinder_mask.size == 0:
            logging.warning("圆柱体mask创建失败")
            return 0.0

        # 获取对应区域的有效骨质mask
        max_i = min_i + cylinder_mask.shape[0]
        max_j = min_j + cylinder_mask.shape[1]
        max_k = min_k + cylinder_mask.shape[2]

        # 确保边界不超出图像范围
        max_i = min(max_i, self.ct_array.shape[0])
        max_j = min(max_j, self.ct_array.shape[1])
        max_k = min(max_k, self.ct_array.shape[2])

        # 提取对应区域的肩胛骨mask（如果存在）
        if self.mask_array is not None:
            local_scapula_mask = self.mask_array[min_i:max_i, min_j:max_j, min_k:max_k]
            # 只使用肩胛骨掩膜作为有效骨质mask
            effective_bone_mask = local_scapula_mask > 0
        else:
            # 如果没有掩膜，认为所有区域都是有效骨质
            effective_bone_mask = np.ones((max_i - min_i, max_j - min_j, max_k - min_k), dtype=bool)

        # 确保mask尺寸匹配
        if effective_bone_mask.shape != cylinder_mask.shape:
            logging.warning(f"Mask尺寸不匹配: {effective_bone_mask.shape} vs {cylinder_mask.shape}")
            # 调整尺寸
            min_shape = tuple(min(a, b) for a, b in zip(effective_bone_mask.shape, cylinder_mask.shape))
            effective_bone_mask = effective_bone_mask[:min_shape[0], :min_shape[1], :min_shape[2]]
            cylinder_mask = cylinder_mask[:min_shape[0], :min_shape[1], :min_shape[2]]

        # 计算交集和覆盖率
        intersection_volume = np.sum(cylinder_mask & effective_bone_mask)
        cylinder_volume = np.sum(cylinder_mask)

        if cylinder_volume == 0:
            logging.warning("圆柱体体积为0")
            return 0.0

        coverage_ratio = intersection_volume / cylinder_volume

        logging.debug(f"圆柱体体积: {cylinder_volume}, 交集体积: {intersection_volume}, 覆盖率: {coverage_ratio:.3f}")

        return coverage_ratio

    def calculate_cylinder_coverage_mask_only(self, path: ScrewPath) -> float:
        """
        计算圆柱体在肩胛骨掩膜内部分的覆盖率
        只考虑从第一个进入mask的点到终点的圆柱体部分

        Args:
            path: 螺钉路径

        Returns:
            mask内部分的覆盖率 (0.0 - 1.0)
        """
        # 获取路径上的采样点，找到第一个进入mask的点
        sample_points = path.get_points_along_path(50)  # 使用较高分辨率

        mask_entry_index = None
        for i, point in enumerate(sample_points):
            if self.is_point_in_mask(point):
                mask_entry_index = i
                break

        if mask_entry_index is None:
            logging.warning("路径完全不进入mask，覆盖率为0")
            return 0.0

        # 计算mask内部分的起始点
        if mask_entry_index == 0:
            # 起始点就在mask内
            mask_start_point = path.start_point
        else:
            # 在mask边界处插值计算精确的进入点
            prev_point = sample_points[mask_entry_index - 1]
            curr_point = sample_points[mask_entry_index]

            # 二分法找到精确的mask边界点
            for _ in range(10):  # 10次迭代足够精确
                mid_point = Point3D(
                    (prev_point.x + curr_point.x) / 2,
                    (prev_point.y + curr_point.y) / 2,
                    (prev_point.z + curr_point.z) / 2
                )
                if self.is_point_in_mask(mid_point):
                    curr_point = mid_point
                else:
                    prev_point = mid_point

            mask_start_point = curr_point

        # 创建mask内部分的路径
        from utils.geometry import ScrewPath
        mask_path = ScrewPath(
            start_point=mask_start_point,
            end_point=path.end_point,
            radius=path.radius,
            length=path.length
        )

        # 计算mask内部分的覆盖率
        return self.calculate_cylinder_coverage(mask_path)

    def evaluate_path_volumetric(self, path: ScrewPath, coverage_threshold: float = 0.95) -> ScrewPath:
        """
        使用体积方法评估螺钉路径（两阶段筛选）

        Args:
            path: 螺钉路径
            coverage_threshold: 覆盖率阈值

        Returns:
            更新后的螺钉路径（包含评估结果）
        """
        # 第一阶段：快速筛选 - 检查终点是否在mask内
        if not self.quick_endpoint_filter(path):
            path.is_valid = False
            path.safety_score = 0.0
            if hasattr(path, 'coverage_ratio'):
                path.coverage_ratio = 0.0
            logging.debug(f"路径终点不在肩胛骨内，快速筛选失败")
            return path

        # 第二阶段：精确筛选 - 计算体积覆盖率
        try:
            coverage_ratio = self.calculate_cylinder_coverage(path)

            # 设置路径属性
            path.coverage_ratio = coverage_ratio
            path.is_valid = coverage_ratio >= coverage_threshold

            # 计算安全评分（基于覆盖率）
            if path.is_valid:
                path.safety_score = coverage_ratio * 100  # 转换为百分比
            else:
                path.safety_score = 0.0

            logging.debug(f"路径覆盖率: {coverage_ratio:.3f}, 有效性: {path.is_valid}")

        except Exception as e:
            logging.error(f"体积评估失败: {e}")
            path.is_valid = False
            path.safety_score = 0.0
            path.coverage_ratio = 0.0

        return path

    def batch_evaluate_paths_volumetric(self, paths: List[ScrewPath],
                                      coverage_threshold: float = 0.95) -> List[ScrewPath]:
        """
        使用体积方法批量评估螺钉路径

        Args:
            paths: 螺钉路径列表
            coverage_threshold: 覆盖率阈值

        Returns:
            评估后的螺钉路径列表
        """
        evaluated_paths = []
        first_stage_filtered = 0
        second_stage_filtered = 0

        logging.info(f"开始体积方法路径评估，共 {len(paths)} 条路径")

        for i, path in enumerate(paths):
            if i % 100 == 0:
                logging.info(f"评估路径进度: {i}/{len(paths)}")

            # 第一阶段快速筛选
            if not self.quick_endpoint_filter(path):
                path.is_valid = False
                path.safety_score = 0.0
                path.coverage_ratio = 0.0
                first_stage_filtered += 1
                evaluated_paths.append(path)
                continue

            # 第二阶段精确评估
            evaluated_path = self.evaluate_path_volumetric(path, coverage_threshold)
            if not evaluated_path.is_valid:
                second_stage_filtered += 1

            evaluated_paths.append(evaluated_path)

        # 统计结果
        valid_paths = [p for p in evaluated_paths if p.is_valid]

        logging.info(f"路径评估完成:")
        logging.info(f"  总路径数: {len(evaluated_paths)}")
        logging.info(f"  第一阶段筛选掉: {first_stage_filtered}")
        logging.info(f"  第二阶段筛选掉: {second_stage_filtered}")
        logging.info(f"  最终有效路径: {len(valid_paths)}")
        logging.info(f"  总体通过率: {len(valid_paths)/len(evaluated_paths)*100:.1f}%")

        return evaluated_paths
